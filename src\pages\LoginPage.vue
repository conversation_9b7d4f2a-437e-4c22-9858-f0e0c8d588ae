<template>
  <q-page class="flex flex-center">
    <q-card flat class="q-pa-md login-card">
      <div class="left-side">
        <div class="logo-container">
          <img src="/icon.png/header.png" alt="Logo" class="logo" />
        </div>
        <div class="company-name">สุขถาวรโอสถ</div>
        <div class="company-tagline">SUKTHAVORN OSOT</div>
      </div>
      <!-- ด้านขวา -->
      <div class="right-side">
        <div class="login-container">
          <q-card-section>
            <img src="/icon.png/user.png" alt="Logo" style="width: 64px" />
          </q-card-section>

          <q-card-section>
            <div class="text-h6">กรุณาเข้าสู่ระบบ</div>
          </q-card-section>

          <q-card-section class="login-section">
            <div class="text-bold q-mb-sm">ชื่อผู้ใช้งาน *</div>
            <q-input
              class="input-container q-mb-md"
              v-model="username"
              dense
              borderless
              label="กรุณากรอกชื่อผู้ใช้งาน"
            />
            <div class="text-bold q-mb-sm">รหัสผ่าน *</div>
            <q-input
              dense
              borderless
              v-model="password"
              label="รหัสผ่าน"
              type="password"
              class="input-container"
            />
          </q-card-section>

          <q-card-actions class="btn-section">
            <q-btn flat label="เข้าสู่ระบบ" class="btn-login" @click="handleLogin" />
          </q-card-actions>
        </div>
      </div>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from 'src/stores/authStore'
import { useUserStore } from 'src/stores/userStore'

const username = ref('')
const password = ref('')
const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()

// ฟังก์ชันสำหรับ login
const handleLogin = async () => {
  if (username.value && password.value) {
    console.log('Logging in user:', username.value)

    try {
      // เรียกใช้ฟังก์ชัน login จาก auth store
      const userInfo = await authStore.login(username.value, password.value)

      // Update user store with current user info
      userStore.setCurrentUser(userInfo)

      // ถ้า login สำเร็จจะไปที่หน้า home
      if (authStore.isAuthenticated) {
        await router.push('/home')
        console.log('✅ Login successful, redirecting to home')
      } else {
        console.error('Authentication failed')
        alert('ชื่อผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง')
      }
    } catch (error) {
      console.error('Login failed:', error)
      alert('ไม่สามารถเข้าสู่ระบบได้')
    }
  } else {
    console.log('Please fill in both fields')
    alert('กรุณากรอกชื่อผู้ใช้งานและรหัสผ่าน')
  }
}
</script>

<style scoped>
.q-page {
  background-image: url('/icon.png/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card {
  display: flex;
  width: 100%;
  max-width: 1000px;
  height: 100%;
  max-height: 600px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  position: relative;
}

.login-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 40%;
  height: 100%;
  background-color: rgba(255, 255, 255, 1);
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.left-side {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 400px;
  background-color: transparent;
  z-index: 1;
}

.right-side {
  flex: 1;
  display: flex;
  background-color: transparent;
}

.login-container {
  width: 565px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
  max-height: 560px;
  margin-left: 16px;
  z-index: 1;
  background-color: #fff;
}

.logo-container {
  margin-bottom: 20px;
}

.logo {
  width: 150px;
  height: 150px;
}

.company-name {
  font-size: 48px;
  font-weight: bold;
  margin-top: 10px;
}

.company-tagline {
  font-size: 18px;
  font-weight: bold;
}

.q-card-section {
  margin-bottom: 20px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: #e1edea;
  border-radius: 8px;
}

.login-section {
  width: 100%;
  max-width: 400px;
}

.btn-login {
  background-color: #91d2c1;
  color: black;
  width: 100%;
  max-width: 200px;
  border-radius: 8px;
}

.btn-section {
  width: 100%;
  max-width: 200px;
}
</style>
