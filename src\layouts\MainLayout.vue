<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated style="background-color: #609fa3; height: 80px">
      <q-toolbar class="full-height items-center">
        <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer" />

        <q-toolbar-title class="text-bold text-h4">{{ currentTitle }}</q-toolbar-title>

        <NotificationBell class="q-mr-md" />
        <UserComponent />
      </q-toolbar>
    </q-header>
    <!-- <q-toolbar v-if="title"></q-toolbar> -->
    <q-drawer v-model="leftDrawerOpen" show-if-above bordered>
      <q-list>
        <div
          class="flex flex-col items-center justify-center relative"
          style="height: 100px; text-align: center"
        >
          <q-item-label header class="text-black flex items-center justify-center relative">
            <q-img
              src="icon.png/header.png"
              width="67px"
              height="67px"
              class="q-mr-sm no-border-radius"
            ></q-img>
            <div class="ml-2">
              <q-item-label
                class="text-bold text-h6"
                style="text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3)"
                >สุขถาวรโอสถ</q-item-label
              >
              <q-item-label style="font-size: 11px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3)">
                SUKTHAVORN OSOT
              </q-item-label>
            </div>
          </q-item-label>
        </div>
        <EssentialLink @updateTitle="updateTitle" />
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
      <AuthDebugInfo />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EssentialLink from 'components/EssentialLink.vue'
import NotificationBell from 'src/components/dialog/NotificationBell.vue'
import UserComponent from 'src/components/userComponent.vue'
import AuthDebugInfo from 'src/components/AuthDebugInfo.vue'

const leftDrawerOpen = ref(false)
const currentTitle = ref('ร้านขายยา')

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

function updateTitle(newTitle: string) {
  currentTitle.value = newTitle
}
</script>
