import type { NavigationGuardNext, RouteLocationNormalized, Router } from 'vue-router'
import { useAuthStore } from 'src/stores/authStore'

/**
 * Authentication guard for protected routes
 */
export const authGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authStore = useAuthStore()

  // Initialize auth state if not already done
  if (!authStore.isAuthenticated && !authStore.token) {
    authStore.initializeAuth()
  }

  // Check if route requires authentication
  const requiresAuth = to.meta.requiresAuth === true

  if (requiresAuth) {
    if (!authStore.isAuthenticated) {
      console.log('🔒 Route requires authentication, redirecting to login')
      next('/login')
      return
    }

    // Check if token is expired
    if (authStore.isTokenExpired) {
      console.log('⏰ Token expired, attempting refresh...')

      try {
        await authStore.refreshToken()
        console.log('✅ Token refreshed successfully')
        next()
      } catch {
        console.log('❌ Token refresh failed, redirecting to login')
        authStore.clearAuth()
        next('/login')
      }
      return
    }

    // Validate token with server if needed
    try {
      const isValid = await authStore.validateToken()
      if (!isValid) {
        console.log('❌ Token validation failed, redirecting to login')
        authStore.clearAuth()
        next('/login')
        return
      }
    } catch {
      console.log('❌ Token validation error, redirecting to login')
      authStore.clearAuth()
      next('/login')
      return
    }
  }

  // If user is authenticated and trying to access login page, redirect to home
  if (to.path === '/login' && authStore.isAuthenticated && !authStore.isTokenExpired) {
    console.log('✅ User already authenticated, redirecting to home')
    next('/home')
    return
  }

  next()
}

/**
 * Setup authentication guards
 */
export const setupAuthGuards = (router: Router) => {
  router.beforeEach(authGuard)

  // Setup token auto-refresh
  const authStore = useAuthStore()
  authStore.setupTokenRefresh()
}
