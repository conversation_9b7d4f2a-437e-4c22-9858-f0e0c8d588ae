import { defineBoot } from '#q-app/wrappers'
import { useAuthStore } from 'src/stores/authStore'

export default defineBoot(() => {
  // Initialize auth store
  const authStore = useAuthStore()

  // Initialize authentication state from localStorage
  authStore.initializeAuth()

  // Setup automatic token refresh
  authStore.setupTokenRefresh()

  console.log('🔐 Auth boot file loaded - authentication system initialized')
})
